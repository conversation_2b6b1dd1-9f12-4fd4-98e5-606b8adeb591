'use client';
import { useState } from 'react';
import ChatContainer from '@/app/components/ChatContainer';
import ChatInput from '@/app/components/ChatInput';

export default function Home() {
  const [messages, setMessages] = useState<
    Array<{ id: string; text: string; sender: 'user' | 'bot' }>
  >([]);

  const handleSendMessage = async (message: string) => {
    // Add user message
    const userMessage = {
      id: Date.now().toString(),
      text: message,
      sender: 'user' as const,
    };

    // Update messages state and capture the current history
    const currentHistory = [...messages, userMessage];
    setMessages(currentHistory);

    const res = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        history: messages, // Send the history before the current message
      }),
    });

    if (!res.body) {
      return;
    }

    const reader = res.body.getReader();
    const decoder = new TextDecoder();
    let fullResponse = '';
    const botMessageId = (Date.now() + 1).toString();

    // Add a placeholder for the bot message
    setMessages((prev) => [
      ...prev,
      { id: botMessageId, text: '...', sender: 'bot' },
    ]);

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        break;
      }
      fullResponse += decoder.decode(value, { stream: true });
      // Update the bot message in the UI as chunks come in
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === botMessageId ? { ...msg, text: fullResponse } : msg
        )
      );
    }
  };

  return (
    <main className="h-screen flex flex-col max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold p-4 border-b">Chatbot MVP</h1>
      <ChatContainer messages={messages} />
      <ChatInput onSendMessage={handleSendMessage} />
    </main>
  );
}
